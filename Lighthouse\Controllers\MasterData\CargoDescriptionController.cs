﻿namespace Lighthouse.Controllers.MasterData
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CargoDescriptionController : ControllerBase
    {
        private readonly ICargoDescriptionService _cargoDescriptionService;
        private readonly IUserService _userService;

        public CargoDescriptionController(ICargoDescriptionService cargoDescriptionService, IUserService userService)
        {
            _cargoDescriptionService = cargoDescriptionService;
            _userService = userService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<CargoDescriptionModel>))]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> GetAllCargoDescriptions()
        {
            var cargoDescriptions = await _cargoDescriptionService.GetCargoDescriptionsAsync();
            return Ok(cargoDescriptions);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoDescriptionModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> GetCargoDescriptionById(Guid id)
        {
            var cargoDescription = await _cargoDescriptionService.GetCargoDescriptionByIdAsync(id);

            return Ok(cargoDescription);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CargoDescriptionModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> CreateCargoDescription([FromBody] CargoDescriptionUpsertModel model)
        {
            var cargoDescription = await _cargoDescriptionService.CreateCargoDescriptionAsync(model, User);

            return Ok(cargoDescription);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoDescriptionModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> UpdateCargoDescription(Guid id, [FromBody] CargoDescriptionUpsertModel model)
        {
            var cargoDescription = await _cargoDescriptionService.UpdateCargoDescriptionAsync(id, model, User);

            return Ok(cargoDescription);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> DeleteCargoDescription(Guid id)
        {
            var deleted = await _cargoDescriptionService.DeleteCargoDescriptionAsync(id);

            return Ok();
        }

        [HttpPost("enableDisable/{id}")]
        [ProducesResponseType(200, Type = typeof(CargoSizeModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> EnableDisableCargoSize(Guid id)
        {
            var user = await _userService.GetCurrentUser(User);
            var size = await _cargoDescriptionService.EnableDisableCargoDescription(id, user);

            return Ok(size);
        }
    }
}
