﻿namespace Lighthouse.Controllers.MasterData
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CargoSizeController : ControllerBase
    {
        private readonly ICargoSizeService service;

        public CargoSizeController(ICargoSizeService service)
        {
            this.service = service;
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<CargoSizeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var cargoSizes = await service.GetAllAsync();
            return Ok(cargoSizes);
        }

        [HttpGet("byFamily")]
        [ProducesResponseType(200, Type = typeof(IList<CargoSizeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByFamily([FromQuery] Guid familyId)
        {
            var cargoFamilySizes = await service.GetByFamilyIdAsync(familyId);
            return Ok(cargoFamilySizes);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoSizeModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Get(Guid id)
        {
            var cargoSize = await service.GetByIdAsync(id);
            return Ok(cargoSize);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CargoSizeModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Post([FromBody] CargoSizeUpsertModel model)
        {
            var cargoSize = await service.CreateAsync(model, User);

            if (cargoSize is null)
            {
                return BadRequest();
            }

            return Ok(cargoSize);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoSizeModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Put(Guid id, [FromBody] CargoSizeUpsertModel model)
        {
            var cargoSize = await service.UpdateAsync(id, model, User);
            return Ok(cargoSize);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await service.DeleteAsync(id);
            return Ok();
        }

        [HttpPost("enableDisable/{id}")]
        [ProducesResponseType(200, Type = typeof(CargoSizeModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> EnableDisableCargoSize(Guid id)
        {
            var size = await service.EnableDisableCargoSize(id);
            return Ok(size);
        }
    }
}
