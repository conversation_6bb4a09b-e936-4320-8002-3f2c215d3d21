﻿namespace Lighthouse.Controllers.Flow
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class FlowVoyageController : ControllerBase
    {
        private readonly IFlowVoyageService _flowVoyageService;

        public FlowVoyageController(IFlowVoyageService flowVoyageService)
        {
            _flowVoyageService = flowVoyageService;
        }

        [HttpGet("getallbylocationid/{locationId}")]
        [ProducesResponseType(200, Type = typeof(IList<FlowVoyageModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllByLocationId(Guid locationId)
        {
            var flowVoyages = await _flowVoyageService.GetAllByLocationIdAsync(locationId);
            return Ok(flowVoyages);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(FlowVoyageModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var flowVoyage = await _flowVoyageService.GetByIdAsync(id);
            return Ok(flowVoyage);
        }

        [HttpPut("recalculatedeckutilisationpercentage/{id}")]
        [ProducesResponseType(200, Type = typeof(FlowVoyageModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> RecalculateDeckUtilisationPercentage(Guid id)
        {
            var flowVoyage = await _flowVoyageService.RecalculateDeckUtilisationPercentage(id);
            return Ok(flowVoyage);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(FlowVoyageModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Post([FromBody] FlowVoyageCreateModel model)
        {
            var flowVoyage = await _flowVoyageService.CreateAsync(model, User);

            if (flowVoyage is null)
            {
                return BadRequest();
            }

            return Ok(flowVoyage);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(FlowVoyageModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Put(Guid id, [FromBody] FlowVoyageUpdateModel model)
        {
            var flowVoyage = await _flowVoyageService.UpdateAsync(id, model, User);

            if (flowVoyage is null)
            {
                return BadRequest();
            }

            return Ok(flowVoyage);
        }

        [HttpPost("validatevoyagenumber")]
        [ProducesResponseType(200, Type = typeof(VoyageNumberValidationResponseModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> ValidateVoyageNumber([FromBody] VoyageNumberValidationRequestModel model)
        {
            var result = await _flowVoyageService.VoyageNumberIsValid(model);

            return Ok(result);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _flowVoyageService.DeleteAsync(id);
            return Ok();
        }

        [HttpGet("releaselist/{id}")]
        [ProducesResponseType(200, Type = typeof(ReleaseListModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetReleaseListInfo(Guid id)
        {
            var releaseList = await _flowVoyageService.GetReleaseListInfo(id);
            return Ok(releaseList);
        }

        [HttpPut("releaselist/{id}")]
        [ProducesResponseType(200, Type = typeof(ReleaseListModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> PutReleaseListInfo(Guid id, [FromBody] ReleaseListUpdateModel model)
        {
            var flowVoyage = await _flowVoyageService.UpdateReleaseListInfo(id, model);

            if (flowVoyage is null)
            {
                return BadRequest(new { Message = "An issue has occurred during the updating of the release list information" });
            }

            return Ok(flowVoyage);
        }

        [HttpPost("sendDepartureEmail")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> SendDepartureEmail([FromBody] DepartureEmailModel model)
        {
            await _flowVoyageService.SendDepartureEmail(model);
            return Ok();
        }

        [HttpPost("sendDiscrepancyReportEmail")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> SendDiscrepancyReportEmail([FromBody] DiscrepancyReportEmailModel model)
        {
            await _flowVoyageService.SendDiscrepancyReportEmail(model);
            return Ok();
        }
    }
}