﻿namespace Lighthouse.Controllers.MasterData
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ClientLocationController: ControllerBase {

        private readonly IClientLocationService clientLocationService;
        private readonly IUserService userService;
        public ClientLocationController(IClientLocationService clientLocationService, IUserService userService) {
            this.clientLocationService = clientLocationService;
            this.userService = userService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<ClientLocationModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var clientLocations = await clientLocationService.GetAsync();
            return Ok(clientLocations);
        }

        [HttpGet("userlocations")]
        [ProducesResponseType(200, Type = typeof(IList<ClientLocationModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByUserClientId() {
            var user = await userService.GetCurrentUser(User);
            var clientLocations = await clientLocationService.GetClientLocationsByClientIdAsync(user.ClientId.Value);
            return Ok(clientLocations);
        }

        [HttpGet("getbylocationid/{locationId}")]
        [ProducesResponseType(200, Type = typeof(IList<ClientLocationModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllByLocationId(Guid locationId) {
            var clientLocations = await clientLocationService.GetClientLocationsByLocationId(locationId);
            return Ok(clientLocations);
        }
    }
}
