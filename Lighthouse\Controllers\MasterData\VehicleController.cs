namespace Lighthouse.Controllers.MasterData {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VehicleController : ControllerBase {
        private readonly IVehicleService _vehicleService;

        public VehicleController(IVehicleService vehicleService) {
            _vehicleService = vehicleService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<VehicleModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllVehicles() {
            var vehicles = await _vehicleService.GetVehiclesAsync();
            return Ok(vehicles);
        }

        [HttpGet("bylocation/{locationId}")]
        [ProducesResponseType(200, Type = typeof(IList<VehicleModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllVehiclesByLocation(Guid locationId, [FromQuery] bool includeAdhoc) {
            var vehicles = await _vehicleService.GetVehiclesByLocationIdAsync(locationId, includeAdhoc);
            return Ok(vehicles);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(VehicleModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetVehicleById(Guid id) {
            var vehicle = await _vehicleService.GetVehicleByIdAsync(id);

            return Ok(vehicle);
        }

        [HttpGet("byregnumber/{locationId}")]
        [ProducesResponseType(200, Type = typeof(VehicleModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByRegistrationNumber(Guid locationId, [FromQuery] string regNumber) {
            var vehicles = await _vehicleService.GetVehiclesByRegistrationNumberAsync(locationId, regNumber);
            return Ok(vehicles);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(VehicleModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> CreateVehicle([FromBody] VehicleUpsertModel model) {
            var vehicle = await _vehicleService.CreateVehicleAsync(model, User);

            return Ok(vehicle);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(VehicleModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> UpdateVehicle(Guid id, [FromBody] VehicleUpsertModel model) {
            var vehicle = await _vehicleService.UpdateVehicleAsync(id, model, User);

            return Ok(vehicle);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> DeleteVehicle(Guid id) {
            var deleted = await _vehicleService.DeleteVehicleAsync(id);

            return Ok();
        }
    }
}
