﻿namespace Lighthouse.Controllers.Voyage {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VoyageCargoLoadController : ControllerBase {
        private readonly IVoyageCargoLoadService _voyageCargoLoadService;
        private readonly IUserService _userService;

        public VoyageCargoLoadController(IVoyageCargoLoadService voyageCargoLoadService, IUserService userService) {
            _voyageCargoLoadService = voyageCargoLoadService;
            _userService = userService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<VoyageCargoLoadModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllCargoLoads() {
            var cargoLoads = await _voyageCargoLoadService.GetVoyageCargoLoadsAsync();
            return Ok(cargoLoads);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoLoadModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetCargoLoadById(Guid id) {
            var cargoLoad = await _voyageCargoLoadService.GetVoyageCargoLoadByIdAsync(id);
            if (cargoLoad == null)
                return NotFound();

            return Ok(cargoLoad);
        }

        [HttpGet("bylocation/{locationId}")]
        [ProducesResponseType(200, Type = typeof(IList<VoyageCargoLoadModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetCargoLoadsByLocationId(Guid locationId) {
            var cargoLoads = await _voyageCargoLoadService.GetVoyageCargoLoadsByLocationIdAsync(locationId);
            return Ok(cargoLoads);
        }

        [HttpGet("getopenload/{locationId}")]
        [ProducesResponseType(200, Type = typeof(IList<VoyageCargoModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetCargoLoadsByLocationId(Guid locationId, [FromQuery] string trailerNumber, [FromQuery] Guid siteId) {
            var cargoLoads = await _voyageCargoLoadService.GetVoyageCargoesForOpenLoadAsync(locationId, trailerNumber, siteId);
            return Ok(cargoLoads);
        }

        [HttpPost("loads/filter")]
        [ProducesResponseType(200, Type = typeof(IList<VoyageCargoQueryModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetVoyageCargoLoadsForDispatchPage([FromBody] VoyageCargoLoadFilterModel filterModel) {
            var user = await _userService.GetCurrentUser();
            var voyageCargos = await _voyageCargoLoadService.VoyageCargoLoadsFiltersAsync(filterModel, user);
            return Ok(voyageCargos);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(VoyageCargoLoadModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CreateCargoLoad([FromBody] VoyageCargoLoadUpsertModel model) {
            var createdCargoLoad = await _voyageCargoLoadService.CreateVoyageCargoLoadAsync(model, User);
            return Ok(createdCargoLoad);

        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoLoadModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateVoyageCargoLoad(Guid id, [FromBody] VoyageCargoLoadUpsertModel model) {
            var updateVoyageCargoLoad = await _voyageCargoLoadService.UpdateVoyageCargoLoadAsync(id, model, User);
            return Ok(updateVoyageCargoLoad);
        }

        [HttpGet("generateconsignmentnotes/{voyageCargoLoadId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GenerateConsignmentNotes(Guid voyageCargoLoadId) {
            var user = await _userService.GetCurrentUser();
            var files = await _voyageCargoLoadService.GenerateConsignmentNote(voyageCargoLoadId, user);

            if (files == null || !files.Any())
                return NotFound("No consignment notes found to generate.");

            using (var memoryStream = new MemoryStream()) {
                using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true)) {
                    foreach (var file in files) {
                        var zipEntry = archive.CreateEntry(file.FileName, CompressionLevel.Fastest);

                        using (var zipStream = zipEntry.Open()) {
                            await zipStream.WriteAsync(file.Content, 0, file.Content.Length);
                        }
                    }
                }

                memoryStream.Position = 0;
                return File(memoryStream.ToArray(), "application/zip", "ConsignmentNotes.zip");
            }
        }

    }
}
