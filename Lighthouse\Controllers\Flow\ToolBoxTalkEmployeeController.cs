﻿namespace Lighthouse.Controllers.Flow
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ToolBoxTalkEmployeeController : Controller
    {
        private readonly IToolBoxTalkEmployeeService _toolBoxTalkEmployeeService;

        public ToolBoxTalkEmployeeController(IToolBoxTalkEmployeeService toolBoxTalkEmployeeService)
        {
            _toolBoxTalkEmployeeService = toolBoxTalkEmployeeService;
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<ToolBoxTalkEmployeeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllToolBoxTalkEmployees()
        {
            var toolBoxTalkEmployees = await _toolBoxTalkEmployeeService.GetAllToolBoxTalkEmployeesAsync();
            return Ok(toolBoxTalkEmployees);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkEmployeeModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetToolBoxTalkEmployeeById(Guid id)
        {
            var toolBoxTalkEmployee = await _toolBoxTalkEmployeeService.GetToolBoxTalkEmployeeByIdAsync(id);
            if (toolBoxTalkEmployee == null)
            {
                return BadRequest(new { Message = "Entity was not found." });
            }
            return Ok(toolBoxTalkEmployee);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkEmployeeModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> CreateToolBoxTalkEmployee([FromBody] ToolBoxTalkEmployeeUpsertModel model)
        {
            var toolBoxTalkEmployee = await _toolBoxTalkEmployeeService.CreateToolBoxTalkEmployeeAsync(model, User);

            if (toolBoxTalkEmployee == null)
            {
                return BadRequest(new { Message = "Creating entity failed, please try again." });
            }

            return Ok(toolBoxTalkEmployee);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkEmployeeModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> UpdateToolBoxTalkEmployee(Guid id, [FromBody] ToolBoxTalkEmployeeUpsertModel model)
        {
            if(model == null)
            {
                return BadRequest(new { Message = "Post body cannot be empty." });
            }
            var toolBoxTalkEmployee = await _toolBoxTalkEmployeeService.UpdateToolBoxTalkEmployeeAsync(id, model, User);

            if (toolBoxTalkEmployee == null)
            {
                return NotFound(new { Message = "Entity was not found." });
            }

            return Ok(toolBoxTalkEmployee);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> DeleteToolBoxTalkEmployee(Guid id)
        {
            var deleted = await _toolBoxTalkEmployeeService.DeleteToolBoxTalkEmployeeAsync(id);
            if (!deleted)
            {
                return BadRequest(new { Message = "Delete operation failed." });
            }
            return Ok();
        }
    }
}
