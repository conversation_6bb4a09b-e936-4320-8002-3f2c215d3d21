namespace Lighthouse.Controllers.MasterData;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class AreaBlockingActivityController : ControllerBase
{
    private readonly IAreaBlockingActivityService _areaBlockingActivityService;
    private readonly IUserService _userService;

    public AreaBlockingActivityController(IAreaBlockingActivityService areaBlockingActivityService,
        IUserService userService)
    {
        _areaBlockingActivityService = areaBlockingActivityService;
        _userService = userService;
    }
    

    [HttpGet]
    [ProducesResponseType(200, Type = typeof(IList<AreaBlockingActivityModel>))]
    [ProducesResponseType(401)]
    public async Task<IActionResult> GetAreaBlockingActivities()
    {
        var areaBlockingActivity = await _areaBlockingActivityService.GetAsync();
        return Ok(areaBlockingActivity);
    }
    
    [HttpGet("{id}")]
    [ProducesResponseType(200, Type = typeof(AreaBlockingActivityModel))]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> GetAreaBlockingActivityById(Guid id)
    {
        var areaBlockingActivity = await _areaBlockingActivityService.GetAreaBlockingActivityIdAsync(id);
        return Ok(areaBlockingActivity);
    }
    
    [HttpGet("byareaid/{id}")]
    [ProducesResponseType(200, Type = typeof(AreaBlockingActivityModel))]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> GetAreaBlockingActivitiesByAreaId(Guid id) {
        var areaBlockingActivity = await _areaBlockingActivityService.GetAreaBlockingActivitiesByAreaId(id);
        return Ok(areaBlockingActivity);
    }
    
    [HttpPost]
    [ProducesResponseType(200, Type = typeof(AreaBlockingActivityModel))]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> Post([FromBody] AreaBlockingActivityUpsertModel model)
    {
        var user = await _userService.GetCurrentUser(User);
        var areaBlockingActivity = await _areaBlockingActivityService.CreateAsync(model, user.UserId);
        return Ok(areaBlockingActivity);
    }

    [HttpPut("{id}")]
    [ProducesResponseType(200, Type = typeof(AreaBlockingActivityModel))]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> Put(Guid id, [FromBody] AreaBlockingActivityUpsertModel model)
    {
        var user = await _userService.GetCurrentUser(User);
        var areaBlockingActivity = await _areaBlockingActivityService.UpdateAsync(id, model, user.UserId);
        if ( areaBlockingActivity is null)
        {
            return BadRequest("Failed to update area blocking activity.");
        }
        
        return Ok(areaBlockingActivity);
    }

    [HttpDelete("{id}")]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _areaBlockingActivityService.DeleteAsync(id);
        return Ok();
    }
}