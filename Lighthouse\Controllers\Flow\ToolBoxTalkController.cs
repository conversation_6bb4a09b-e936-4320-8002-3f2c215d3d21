﻿namespace Lighthouse.Controllers.Flow
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ToolBoxTalkController : ControllerBase
    {        
        private readonly IToolBoxTalkService _toolBoxTalkService;

        public ToolBoxTalkController(IToolBoxTalkService toolBoxTalkService) {
            _toolBoxTalkService = toolBoxTalkService;
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<ToolBoxTalkModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllToolBoxTalks([FromQuery] Guid? locationId) {
            var toolBoxTalks = await _toolBoxTalkService.GetAllToolBoxTalksAsync(locationId);
            return Ok(toolBoxTalks);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetToolBoxTalkById(Guid id) {
            var toolBoxTalk = await _toolBoxTalkService.GetToolBoxTalkByIdAsync(id);
            if (toolBoxTalk == null) {
                return BadRequest(new { Message = "Entity was not found." });
            }
            return Ok(toolBoxTalk);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.Foreman)]
        public async Task<IActionResult> CreateToolBoxTalk([FromBody] ToolBoxTalkUpsertModel model) {
            var toolBoxTalk = await _toolBoxTalkService.CreateToolBoxTalkAsync(model, User);

            if (toolBoxTalk == null) {
                return BadRequest(new { Message = "Creating entity failed, please try again." });
            }

            return Ok(toolBoxTalk);
        }

        [HttpPost("createrevision/{id}")]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.Foreman,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> CreateToolBoxTalkRevision(string id)
        {
            var toolBoxTalk = await _toolBoxTalkService.CreateToolBoxTalkRevisionAsync(Guid.Parse(id), User);

            if (toolBoxTalk == null)
            {
                return BadRequest(new { Message = "Creating entity revision failed, please try again." });
            }

            return Ok(toolBoxTalk);
        }


        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.Foreman,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> UpdateToolBoxTalk(Guid id, [FromBody] ToolBoxTalkUpsertModel model) {
            var toolBoxTalk = await _toolBoxTalkService.UpdateToolBoxTalkAsync(id, model, User);

            if (toolBoxTalk == null) {
                return BadRequest(new { Message = "Updating entity failed." });
            }

            return Ok(toolBoxTalk);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> DeleteToolBoxTalk(Guid id) {
            var deleted = await _toolBoxTalkService.DeleteToolBoxTalkAsync(id);
            if (!deleted) {
                return BadRequest(new { Message = "Delete operation failed." });
            }
            return Ok();
        }
    }
}
