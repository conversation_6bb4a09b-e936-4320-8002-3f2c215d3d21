﻿namespace Lighthouse.Controllers.MasterData
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class DangerousGoodController : ControllerBase
    {
        private readonly IDangerousGoodService dangerousGoodService;

        public DangerousGoodController(IDangerousGoodService dangerousGoodService)
        {
            this.dangerousGoodService = dangerousGoodService;
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<DangerousGoodModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var dangerousGoods = await dangerousGoodService.GetAllAsync();
            return Ok(dangerousGoods);
        }

        [HttpGet("list/{locationId}")]
        [ProducesResponseType(200, Type = typeof(IList<DangerousGoodModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetList(Guid locationId)
        {
            var dangerousGoods = await dangerousGoodService.GetListByLocationIdAsync(locationId);
            return Ok(dangerousGoods);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(DangerousGoodModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var dangerousGood = await dangerousGoodService.GetDangerousGoodByIdAsync(id);
            return Ok(dangerousGood);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(DangerousGoodModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Post([FromBody] DangerousGoodUpsertModel model)
        {
            var dangerousGood = await dangerousGoodService.CreateAsync(model, User);

            if (dangerousGood is null)
            {
                return BadRequest();
            }

            return Ok(dangerousGood);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(DangerousGoodModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Put(Guid id, [FromBody] DangerousGoodUpsertModel model)
        {
            var dangerousGood = await dangerousGoodService.UpdateAsync(id, model, User);
            return Ok(dangerousGood);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await dangerousGoodService.DeleteAsync(id);
            return Ok();
        }
    }
}
