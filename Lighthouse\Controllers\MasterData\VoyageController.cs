﻿namespace Lighthouse.Controllers.MasterData
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VoyageController : ControllerBase {
        private readonly IVoyageService voyageService;
        private readonly IUserService userService;
        private readonly IVoyageImportService voyageImportService;

        public VoyageController(IVoyageService voyageService,
            IUserService userService,
            IVoyageImportService voyageImportService) {
            this.voyageService = voyageService;
            this.userService = userService;
            this.voyageImportService = voyageImportService;
        }

        [HttpGet("voyage-template")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetVoyageTemplate() {
            byte[] data = await voyageService.DownloadVoyageTemplate();

            return File(data, "application/download");
        }

        [HttpPost("import")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> ImportVoyages(IFormFile file) {
            var user = await userService.GetCurrentUser(User);

            byte[] data = await voyageImportService.ImportVoyages(file, user.UserId, user.LocationTimeZoneInfoId);

            return File(data, "application/download");
        }

        [HttpGet("exportvoyages")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> ExportVoyages() {
            try {
                var user = await userService.GetCurrentUser(User);

                byte[] data = await voyageService.ExportVoyages(user.LocationTimeZoneInfoId);

                return File(data, "application/download");
            } catch (Exception) {
                return NotFound();
            }
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<VoyageModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllAsync([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate) {
            var voyages = await voyageService.GetAsync(startDate, endDate);
            return Ok(voyages);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var voyage = await voyageService.GetVoyageByIdAsync(id);
            return Ok(voyage);
        }

        [HttpGet("getAssignedActiveVoyagesByLocationId/{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetTransportRequestAssignedActiveVoyagesByLocationId(Guid id, bool? status)
        {
            var voyages = await voyageService.GetTransportRequestAssignedActiveVoyagesByLocationId(id, status);
            return Ok(voyages);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(VoyageModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Post([FromBody] VoyageUpsertModel model) {
            var user = await userService.GetCurrentUser(User);

            if (model is null) {
                return BadRequest("Something went wrong when attempting to create Voyage");
            }

            var voyage = await voyageService.PostAsync(model, user.UserId);

            if (voyage is null) {
                return BadRequest("Failed to create Voyage");
            }

            return Ok(voyage);
        }

        [HttpPost("voyagecomplete/{id}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> CompleteVoyage(Guid id)
        {

            var user = await userService.GetCurrentUser(User);

            byte[] data = await voyageService.CompleteAsync(id, user.UserId);

            return File(data, "application/download");
        }

        [HttpPut("voyageincomplete/{id}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> IncompleteVoyage(Guid id) {

            var user = await userService.GetCurrentUser(User);

            var voyage = await voyageService.IncompleteAsync(id, user.UserId);

            if (voyage is null) {
                return BadRequest("Failed to update Voyage");
            }

            return Ok(voyage);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Put(Guid id, [FromBody] VoyageUpsertModel model) {           

            var user = await userService.GetCurrentUser(User);            

            if (model is null) {
                return NotFound("Something went wrong when attempting to update Voyage");
            }

            var voyage = await voyageService.PutAsync(id, model, user.UserId);

            if (voyage is null) {
                return BadRequest("Failed to update Voyage");
            }

            return Ok(voyage);
        }

        [HttpPut("updateBillingPeriod/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> UpdateVoyageBillingPeriod(Guid id, [FromBody] ChangeVoyageBillingPeriodModel model) {
            await voyageService.UpdateVoyageBillingPeriod(id, model);

            return Ok();                                    
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Delete(Guid id) {
            var user = await userService.GetCurrentUser(User);

            var isVoyageDeleted = await voyageService.DeleteAsync(id, user.UserId);
            if (!isVoyageDeleted) {
                return BadRequest("Failed to Delete Voyage");
            }
            return Ok();
        }
    }
}
