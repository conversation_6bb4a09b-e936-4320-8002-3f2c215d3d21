﻿namespace Lighthouse.Controllers.MasterData
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ClientController: ControllerBase {
        private readonly IClientService clientService;
        private readonly IUserService userService;
        private readonly IAzureBlobStorageService storageService;

        public ClientController(
            IClientService clientService,
            IUserService userService,
            IAzureBlobStorageService storageService
            ) {
            this.clientService = clientService;
            this.userService = userService;
            this.storageService = storageService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<ClientModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var clients = await clientService.GetAsync();
            return Ok(clients);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(ClientModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var client = await clientService.GetClientByIdAsync(id);
            return Ok(client);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(ClientModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin , UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Post([FromForm] ClientUpsertModel model) {
            var user = await userService.GetCurrentUser(User);

            if (model is null) {
                return BadRequest();
            }

            var client = await clientService.PostAsync(model, user.UserId);

            if (client is null) {
                return BadRequest();
            }

            return Ok(client);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(ClientModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin , UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Put(Guid id, [FromForm] ClientUpsertModel model) {

            var user = await userService.GetCurrentUser(User);

            if (model is null) {
                return NotFound();
            }

            var client = await clientService.PutAsync(id, model, user.UserId);

            if (client is null) {
                return BadRequest();
            }

            return Ok(client);
        }


        [HttpGet("photo/{id}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetPhoto(Guid id)
        {
            if (!await storageService.ExistsAsync(BlobStorageFolderConstant.Client, id))
            {
                return NotFound(new { Message = "Record was not found." });
            }

            var download = await storageService.DownloadAsync(BlobStorageFolderConstant.Client, id);
            return File(download.Item1, download.Item2);
        }
    }
}
