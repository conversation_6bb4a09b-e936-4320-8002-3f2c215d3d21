﻿namespace Lighthouse.Middleware.Tenant
{
    public static class TenantHandlerMiddlewareExtensions
    {
        public static void UseTenantSwitcherMiddleware(this IApplicationBuilder app)
        {
            app.Use(async (context, next) =>
            {
                var tenantsInformation = app.ApplicationServices.GetRequiredService<TenantsInformation>();
                string tenantName = "";
                string tenantId = "";
                var referer = context.Request.Headers["Referer"].ToString();

                if(string.IsNullOrEmpty(referer) || referer.EndsWith("/swagger/index.html"))
                {
                    await next();
                    return;
                }

                if (context.User.Identity.IsAuthenticated) {
                    var emailClaim = context.User.Claims.FirstOrDefault(x => x.Type == "https://lighthouse.peterson.com/claims/email");
                    if (emailClaim != null) {
                        context.User.AddIdentity(new ClaimsIdentity(new[] { new Claim(ClaimTypes.Email, emailClaim.Value) }));
                    }
                }

                var tenantEndIndex = referer.IndexOf('.'); // https://{tenantName}.whatever.abc
                tenantName = referer.Substring(8, tenantEndIndex - 8); // 8: "https://".Length



                var orgIdClaim = context.User.Claims.FirstOrDefault(x => x.Type == "org_id")?.Value;
                var wrongTenant = (string.IsNullOrEmpty(orgIdClaim) || !tenantsInformation.Tenants.TryGetValue(tenantName, out var orgInfo) || orgInfo.Id != orgIdClaim);
                var isGlobalUser = orgIdClaim == tenantsInformation.AdminOrganization.Id;

                if (context.User.Identity.IsAuthenticated && wrongTenant && !isGlobalUser)//TODO: after connecting admin app events, here we should check default tenant as well
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Forbidden;

                    await context.Response.WriteAsync("Forbidden. Missing or invalid tenant identification.");
                    return;
                }

                context.Items["TENANT_NAME"] = tenantName;
                context.Items["TENANT_ID"] = tenantId;
                await next();

            });
        }
    }
}
