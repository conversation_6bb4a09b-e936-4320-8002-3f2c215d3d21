﻿namespace Lighthouse.Controllers.MasterData {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ClusterController: ControllerBase {
        private readonly IClusterService _clusterService;

        public ClusterController(IClusterService clusterService) {
            _clusterService = clusterService;
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<ClusterModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAll() {
            var clusters = await _clusterService.GetAllClustersAsync();
            return Ok(clusters);
        }

        [HttpGet("{clusterHeadId}/clusterhistory")]
        [ProducesResponseType(200, Type = typeof(IList<ClusterModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetClusterChildrenForClusters(Guid clusterHeadId) {
            var clusters = await _clusterService.GetAllClusterHistoryForClusters(clusterHeadId);
            return Ok(clusters);
        }

        [HttpGet("{assetId}/assethistory")]
        [ProducesResponseType(200, Type = typeof(IList<ClusterModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetClusterChildrenForAsset(Guid assetId) {
            var clusters = await _clusterService.GetAllClusterHistoryForAsset(assetId);
            return Ok(clusters);
        }

    }
}
