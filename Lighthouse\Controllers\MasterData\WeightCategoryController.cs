﻿namespace Lighthouse.Controllers.MasterData
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class WeightCategoryController : ControllerBase
    {
        private readonly IWeightCategoryService _weightCategoryService;
        private readonly IUserService _userService;

        public WeightCategoryController(IWeightCategoryService weightCategory, IUserService userService)
        {
            _weightCategoryService = weightCategory;
            _userService = userService;
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<WeightCategoryModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllWeightCategories()
        {
            var weightCategories = await _weightCategoryService.GetAllWeightCategoriesAsync();
            return Ok(weightCategories);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(WeightCategoryModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Get(Guid id)
        {
            var weightCategory = await _weightCategoryService.GetByIdAsync(id);
            return Ok(weightCategory);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(WeightCategoryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Post([FromBody] WeightCategoryCreateModel model)
        {
            var user = await _userService.GetCurrentUser();
            var weightCategory = await _weightCategoryService.CreateAsync(model, user);

            if (weightCategory is null)
            {
                return BadRequest();
            }

            return Ok(weightCategory);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(WeightCategoryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Put(Guid id, [FromBody] WeightCategoryUpdateModel model)
        {
            var user = await _userService.GetCurrentUser();
            var weightCategory = await _weightCategoryService.UpdateAsync(id, model, user);

            if (weightCategory is null)
            {
                return BadRequest();
            }

            return Ok(weightCategory);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _weightCategoryService.DeleteAsync(id);
            return Ok();
        }

        [HttpGet("bylocation/{locationId}/{voyageId}")]
        [ProducesResponseType(200, Type = typeof(IList<VendorModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllWeightCategoriesByLocationAndVoyage(Guid locationId, Guid voyageId)
        {
            var vendors = await _weightCategoryService.GetAllByLocationAndVoyageAsync(locationId, voyageId);
            return Ok(vendors);
        }
    }
}
