﻿namespace Lighthouse.Controllers.Flow
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VoyageCommentController : ControllerBase
    {
        private readonly IVoyageCommentService _voyageCommentService;

        public VoyageCommentController(IVoyageCommentService voyageCommentService)
        {
            _voyageCommentService = voyageCommentService;
        }

        [HttpGet("list/{voyageId}")]
        [ProducesResponseType(200, Type = typeof(IList<VoyageCommentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAll(Guid voyageId)
        {
            var voyageComments = await _voyageCommentService.GetAllAsync(voyageId);
            return Ok(voyageComments);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCommentModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var voyageComment = await _voyageCommentService.GetByIdAsync(id);
            return Ok(voyageComment);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(VoyageCommentModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Post([FromBody] VoyageCommentUpsertModel model)
        {

            var voyageComment = await _voyageCommentService.CreateAsync(model, User);

            if (voyageComment is null)
            {
                return BadRequest();
            }

            return Ok(voyageComment);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCommentModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Put(Guid id, [FromBody] VoyageCommentUpsertModel model)
        {
            var voyageComment = await _voyageCommentService.UpdateAsync(id, model, User);

            if (voyageComment is null)
            {
                return BadRequest();
            }

            return Ok(voyageComment);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _voyageCommentService.DeleteAsync(id);
            return Ok();
        }
    }
}

