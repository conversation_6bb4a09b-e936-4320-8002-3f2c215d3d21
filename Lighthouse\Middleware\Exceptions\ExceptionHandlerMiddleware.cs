﻿namespace Lighthouse.Middleware.Exceptions {
    public class ExceptionHandlerMiddleware {
        private readonly RequestDelegate _next;

        public ExceptionHandlerMiddleware(RequestDelegate next) {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, ILogger<ExceptionHandlerMiddleware> logger) {
            try {
                await _next(context);
            }
            catch (Exception ex) {
                await HandleExceptionAsync(context, ex, logger);
            }
        }

        private static Task HandleExceptionAsync(HttpContext context, Exception exception, ILogger logger) {

            logger.LogError(exception, $@"Error happened. Details:

            Tenant: {context.Items["TENANT_NAME"]}
            Application: {context.Request.Headers["Application"].FirstOrDefault()}
            User Email: {context.User.Claims.FirstOrDefault(x=>x.Type == "https://lighthouse.peterson.com/claims/email")?.Value}
            Path: {context.Request.Path}
            Endpoint: {context.GetEndpoint()?.DisplayName}
            Method: {context.Request.Method}
            Request Content Length: {context.Request.ContentLength}
            ");

            HttpStatusCode status = HttpStatusCode.InternalServerError;
            string message = "An unexpected error occurred.";

            // You can add more custom exception handling here if needed

            context.Response.ContentType = "application/json";
            context.Response.StatusCode = (int)status;

            var errorResponse = new {
                status = (int)status,
                message = message,
                details = exception.Message
            };

            string result = System.Text.Json.JsonSerializer.Serialize(errorResponse);
            return context.Response.WriteAsync(result);
        }
    }
}
