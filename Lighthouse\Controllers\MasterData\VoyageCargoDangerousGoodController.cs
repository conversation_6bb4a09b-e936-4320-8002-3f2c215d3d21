﻿namespace Lighthouse.Controllers.MasterData
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VoyageCargoDangerousGoodController : ControllerBase
    {
        private readonly IVoyageCargoDangerousGoodService voyageCargoDangerousGoodService;

        public VoyageCargoDangerousGoodController(IVoyageCargoDangerousGoodService voyageCargoDangerousGoodService)
        {
            this.voyageCargoDangerousGoodService = voyageCargoDangerousGoodService;
        }

        [HttpGet("list/{voyageCargoId}")]
        [ProducesResponseType(200, Type = typeof(IList<VoyageCargoDangerousGoodModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetList(Guid voyageCargoId)
        {
            var voyageCargoDangerousGoods = await voyageCargoDangerousGoodService.GetListAsync(voyageCargoId);
            return Ok(voyageCargoDangerousGoods);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoDangerousGoodModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var voyageCargoDangerousGood = await voyageCargoDangerousGoodService.GetVoyageCargoDangerousGoodByIdAsync(id);
            return Ok(voyageCargoDangerousGood);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(VoyageCargoDangerousGoodModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Post([FromBody] VoyageCargoDangerousGoodUpsertModel model)
        {
            var voyageCargoDangerousGood = await voyageCargoDangerousGoodService.CreateAsync(model);

            if (voyageCargoDangerousGood is null)
            {
                return BadRequest();
            }

            return Ok(voyageCargoDangerousGood);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoDangerousGoodModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Put(Guid id, [FromBody] VoyageCargoDangerousGoodUpsertModel model)
        {
            var voyageCargoDangerousGood = await voyageCargoDangerousGoodService.UpdateAsync(id, model);
            return Ok(voyageCargoDangerousGood);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await voyageCargoDangerousGoodService.DeleteAsync(id);
            return Ok();
        }
    }
}
