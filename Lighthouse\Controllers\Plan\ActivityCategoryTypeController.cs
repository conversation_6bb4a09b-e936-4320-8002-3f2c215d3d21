namespace Lighthouse.Controllers.Plan;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class ActivityCategoryTypeController : ControllerBase
{
    private readonly IActivityCategoryTypeService _activityCategoryTypeService;
    private readonly IUserService _userService;

    public ActivityCategoryTypeController(IActivityCategoryTypeService activityCategoryTypeService,
        IUserService userService)
    {
        _activityCategoryTypeService = activityCategoryTypeService;
        _userService = userService;
    }

    [HttpGet]
    [ProducesResponseType(200, Type = typeof(IList<ActivityCategoryTypeModel>))]
    [ProducesResponseType(401)]
    public async Task<IActionResult> GetActivityCategoryTypes()
    {
        var activityCategoryType = await _activityCategoryTypeService.GetAsync();
        return Ok(activityCategoryType);
    }

    [HttpGet("activityCategoryType/{id}")]
    [ProducesResponseType(200, Type = typeof(ActivityCategoryTypeModel))]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> GetActivityCategoryTypeById(Guid id)
    {
        var activityCategoryType = await _activityCategoryTypeService.GetCategoryTypeIdAsync(id);
        return Ok(activityCategoryType);
    }

    [HttpGet("bycategoryid/{id}")]
    [ProducesResponseType(200, Type = typeof(ActivityCategoryTypeModel))]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> GetAllCategoryTypesByCategoryId(Guid id)
    {
        var categoryType = await _activityCategoryTypeService.GetCategoryTypesByCategoryId(id);
        return Ok(categoryType);
    }

    [HttpPost]
    [ProducesResponseType(200, Type = typeof(ActivityCategoryTypeModel))]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> Post([FromBody] ActivityCategoryTypeUpsertModel model)
    {
        var user = await _userService.GetCurrentUser(User);
        var activityCategoryType = await _activityCategoryTypeService.PostAsync(model, user.UserId);
        return Ok(activityCategoryType);
    }

    [HttpPut("{id}")]
    [ProducesResponseType(200, Type = typeof(ActivityCategoryTypeModel))]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> Put(Guid id, [FromBody] ActivityCategoryTypeUpsertModel model)
    {
        var user = await _userService.GetCurrentUser(User);
        var activityCategoryType = await _activityCategoryTypeService.PutAsync(id, model, user.UserId);
        if (activityCategoryType is null)
        {
            return BadRequest("Failed to update activityCategory.");
        }

        return Ok(activityCategoryType);
    }

    [HttpDelete("{id}")]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _activityCategoryTypeService.DeleteAsync(id);
        return Ok();
    }

}