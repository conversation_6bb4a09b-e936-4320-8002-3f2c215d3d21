﻿namespace Lighthouse.Controllers.MasterData {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CraneController : ControllerBase {
        private readonly ICraneService _craneService;

        public CraneController(ICraneService craneService) {
            _craneService = craneService;
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<CraneModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAll() {
            var cranes = await _craneService.GetCraneListAsync();
            return Ok(cranes);
        }

        [HttpGet("bylocationid/{locationId}")]
        [ProducesResponseType(200, Type = typeof(CraneModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllByLocation(Guid locationId) {
            var cranes = await _craneService.GetCraneListByLocationIdAsync(locationId);
            return Ok(cranes);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CraneModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var crane = await _craneService.GetCraneByIdAsync(id);
            if (crane == null) {
                return NotFound();
            }
            return Ok(crane);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CraneModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Post([FromBody] CraneUpsertModel model) {
            var crane = await _craneService.CreateCraneAsync(model, User);

            if (crane == null) {
                return BadRequest();
            }

            return Ok(crane);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CraneModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Put(Guid id, [FromBody] CraneUpsertModel model) {
            var crane = await _craneService.UpdateCraneAsync(id, model, User);

            if (crane == null) {
                return BadRequest();
            }

            return Ok(crane);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Delete(Guid id) {
            var deleted = await _craneService.DeleteCraneAsync(id);
            if (!deleted) {
                return NotFound();
            }
            return Ok();
        }
    }
}
