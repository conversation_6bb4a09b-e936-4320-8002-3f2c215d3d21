﻿namespace Lighthouse.Controllers.Flow
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ToolBoxTalkSiteController : Controller
    {
        private readonly IToolBoxTalkSiteService _toolBoxTalkSiteService;

        public ToolBoxTalkSiteController(IToolBoxTalkSiteService toolBoxTalkSiteService)
        {
            _toolBoxTalkSiteService = toolBoxTalkSiteService;
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<ToolBoxTalkSiteModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllToolBoxTalkSites()
        {
            var toolBoxTalkSites = await _toolBoxTalkSiteService.GetAllToolBoxTalkSitesAsync();
            return Ok(toolBoxTalkSites);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkSiteModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetToolBoxTalkSiteById(Guid id)
        {
            var toolBoxTalkSite = await _toolBoxTalkSiteService.GetToolBoxTalkSiteByIdAsync(id);
            if (toolBoxTalkSite == null)
            {
                return BadRequest(new { Message = "Entity was not found." });
            }
            return Ok(toolBoxTalkSite);
        }

        [HttpPost("getareasbytoolboxtalk")]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkSiteModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> GetToolBoxTalkSitesByToolBoxTalkId([FromBody] IList<Guid> ids)
        {
            var toolBoxTalkSites = await _toolBoxTalkSiteService.GetToolBoxTalkSitesByToolBoxTalkIdAsync(ids);
            if (toolBoxTalkSites == null || !ids.Any())
            {
                return BadRequest(new { Message = "Query failed." });
            }
            return Ok(toolBoxTalkSites);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkSiteModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> CreateToolBoxTalkSite([FromBody] ToolBoxTalkSiteUpsertModel model)
        {
            var toolBoxTalkSite = await _toolBoxTalkSiteService.CreateToolBoxTalkSiteAsync(model, User);

            if (toolBoxTalkSite == null)
            {
                return BadRequest(new { Message = "Creating entity failed, please try again." });
            }

            return Ok(toolBoxTalkSite);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(ToolBoxTalkSiteModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> UpdateToolBoxTalkSite(Guid id, [FromBody] ToolBoxTalkSiteUpsertModel model)
        {
            var toolBoxTalkSite = await _toolBoxTalkSiteService.UpdateToolBoxTalkSiteAsync(id, model, User);

            if (toolBoxTalkSite == null)
            {
                return BadRequest(new { Message = "Updating eitity failed." });
            }

            return Ok(toolBoxTalkSite);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> DeleteToolBoxTalkSite(Guid id)
        {
            var deleted = await _toolBoxTalkSiteService.DeleteToolBoxTalkSiteAsync(id);
            if (!deleted)
            {
                return BadRequest(new { Message = "Delete operation failed." });
            }
            return Ok();
        }
    }
}
