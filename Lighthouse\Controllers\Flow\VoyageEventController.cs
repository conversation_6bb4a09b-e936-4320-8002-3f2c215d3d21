﻿namespace Lighthouse.Controllers.Flow
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VoyageEventController : ControllerBase
    {
        private readonly IVoyageEventService _voyageEventService;

        public VoyageEventController(IVoyageEventService voyageEventService)
        {
            _voyageEventService = voyageEventService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<VoyageEventModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAll()
        {
            var voyageEvents = await _voyageEventService.GetAllAsync();
            return Ok(voyageEvents);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageEventModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var voyageEvent = await _voyageEventService.GetByIdAsync(id);
            return Ok(voyageEvent);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(VoyageEventModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Post([FromBody] VoyageEventCreateModel model)
        {

            var voyageEvent = await _voyageEventService.CreateAsync(model, User);

            if (voyageEvent is null)
            {
                return BadRequest();
            }

            return Ok(voyageEvent);
        }
    }
}

