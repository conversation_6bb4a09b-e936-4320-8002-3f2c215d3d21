﻿namespace Lighthouse.Controllers.Plan
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VoyageCargoSailingRequestActivityController : ControllerBase
    {
        private readonly IVoyageCargoSailingRequestActivityService _voyageCargoSailingRequestActivityService;

        public VoyageCargoSailingRequestActivityController(IVoyageCargoSailingRequestActivityService voyageCargoSailingRequestActivityService)
        {
            _voyageCargoSailingRequestActivityService = voyageCargoSailingRequestActivityService;
        }

        [HttpGet("getallbyysailingrequestactivityid/{sailingRequestActivityId}")]
        [ProducesResponseType(200, Type = typeof(IList<VoyageCargoSailingRequestActivityModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllByySailingRequestActivityId(Guid sailingRequestActivityId)
        {
            var voyageCargoSailingRequestActivitys = await _voyageCargoSailingRequestActivityService.GetAllBySailingRequestActivityIdAsync(sailingRequestActivityId);
            return Ok(voyageCargoSailingRequestActivitys);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoSailingRequestActivityModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Get(Guid id)
        {
            var voyageCargoSailingRequestActivity = await _voyageCargoSailingRequestActivityService.GetByIdAsync(id);
            return Ok(voyageCargoSailingRequestActivity);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(VoyageCargoSailingRequestActivityModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Post([FromBody] VoyageCargoSailingRequestActivityUpsertModel model)
        {

            var voyageCargoSailingRequestActivity = await _voyageCargoSailingRequestActivityService.CreateAsync(model, User);

            if (voyageCargoSailingRequestActivity is null)
            {
                return BadRequest();
            }

            return Ok(voyageCargoSailingRequestActivity);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoSailingRequestActivityModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Put(Guid id, [FromBody] VoyageCargoSailingRequestActivityUpsertModel model)
        {
            var voyageCargoSailingRequestActivity = await _voyageCargoSailingRequestActivityService.UpdateAsync(id, model, User);

            if (voyageCargoSailingRequestActivity is null)
            {
                return BadRequest();
            }

            return Ok(voyageCargoSailingRequestActivity);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _voyageCargoSailingRequestActivityService.DeleteAsync(id);
            return Ok();
        }
    }
}

