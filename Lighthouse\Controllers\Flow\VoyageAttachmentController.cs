﻿namespace Lighthouse.Controllers.Flow
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VoyageAttachmentController : ControllerBase
    {
        private readonly IVoyageAttachmentService voyageAttachmentService;

        public VoyageAttachmentController(IVoyageAttachmentService voyageAttachmentService)
        {
            this.voyageAttachmentService = voyageAttachmentService;
        }

        [RequestFormLimits(MultipartBodyLengthLimit = Int32.MaxValue, ValueLengthLimit = Int32.MaxValue)]
        [DisableRequestSizeLimit]
        [HttpPost("{voyageId}")]
        [ProducesResponseType(200, Type = typeof(Guid))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<ActionResult<Guid>> Post(Guid voyageId, [FromForm] VoyageAttachmentCreateModel model)
        {
            return await voyageAttachmentService.AddAttachmentAsync(voyageId, model);
        }

        [RequestFormLimits(MultipartBodyLengthLimit = Int32.MaxValue, ValueLengthLimit = Int32.MaxValue)]
        [DisableRequestSizeLimit]
        [HttpPost("bulk/{voyageId}")]
        [ProducesResponseType(200, Type = typeof(Guid))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<ActionResult<Guid>> BulkUpload(Guid voyageId, [FromForm] VoyageAttachmentBulkCreateModel model)
        {
            await voyageAttachmentService.AddBulkAttachmentAsync(voyageId, model);
            return NoContent();
        }

        [HttpGet("{voyageId}")]
        [ProducesResponseType(200, Type = typeof(List<VoyageAttachmentModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetVoyageAttachmentAsync(Guid voyageId,[FromQuery] VoyageAttachmentType? voyageAttachmentType)
        {
            var downloads = await voyageAttachmentService.GetAttachmentAsync(voyageId, voyageAttachmentType);

            if (downloads == null || !downloads.Any())
            {
                return Ok();
            }

            return Ok(downloads);
        }

        [HttpDelete("{blobId}/{voyageId}")]
        [ProducesResponseType(200, Type = typeof(Guid))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<ActionResult<Guid>> DeleteVoyageAttachmentAsync(Guid blobId, Guid voyageId)
        {
            return await voyageAttachmentService.DeleteAttachmentAsync(blobId, voyageId);
        }

        [HttpGet("{blobId}/{voyageId}")]
        public async Task<IActionResult> DownloadDocument(Guid blobId, Guid voyageId)
        {
            try
            {
                var (fileData, contentType, attachment) = await voyageAttachmentService.DownloadDocumentAsync(blobId, voyageId);
                Response.Headers.Append("Content-Disposition", $"attachment; filename=\"{attachment.FileName}\"");
                return File(fileData, contentType);
            }
            catch
            {
                throw new Exception("Error encountered when retrieving attachment");
            }
        }
    }
}
