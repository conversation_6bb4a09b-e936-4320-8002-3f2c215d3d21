﻿namespace Lighthouse.Middleware.ValidateModel {
    public static class ValidateModelMiddlewareExtensions {
        public static void UseValidateModelMiddleware(this IApplicationBuilder app) {
            app.Use(async (context, next) => {
                if (context.Request.Method == Microsoft.AspNetCore.Http.HttpMethods.Post || context.Request.Method == Microsoft.AspNetCore.Http.HttpMethods.Put) {
                    var form = await context.Request.ReadFormAsync();
                    var model = form.ToDictionary(pair => pair.Key, pair => pair.Value.FirstOrDefault());
                    var validationContext = new ValidationContext(model);
                    var validationResults = new List<ValidationResult>();
                    Validator.TryValidateObject(model, validationContext, validationResults, true);
                    if (validationResults.Count > 0) {
                        var problemDetails = new ValidationProblemDetails(validationResults.ToDictionary(x => x.MemberNames.FirstOrDefault() ?? string.Empty, x => new[] { x.ErrorMessage })) {
                            Type = "https://tools.ietf.org/html/rfc7231#section-6.5.1",
                            Title = "One or more validation errors occurred.",
                            Status = (int)HttpStatusCode.BadRequest,
                            Detail = "See the 'errors' property for details."
                        };
                        context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                        context.Response.ContentType = "application/json";
                        await context.Response.WriteAsJsonAsync(problemDetails);
                        return;
                    }
                }
                await next();
            });
        }
    }
}
