﻿namespace Lighthouse.Controllers.Flow
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CargoCertificateController : ControllerBase
    {
        private readonly ICargoCertificateService _cargoCertificateService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;

        public CargoCertificateController(ICargoCertificateService cargoCertificateService, IAzureBlobStorageService azureBlobStorageService)
        {
            _cargoCertificateService = cargoCertificateService;
            _azureBlobStorageService = azureBlobStorageService;
        }

        [HttpGet("list")]
        [ProducesResponseType(200, Type = typeof(IList<CargoCertificateModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAll()
        {
            var cargoCertificates = await _cargoCertificateService.GetAllAsync();
            return Ok(cargoCertificates);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoCertificateModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var cargoCertificate = await _cargoCertificateService.GetByIdAsync(id);
            return Ok(cargoCertificate);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CargoCertificateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Post([FromBody] CargoCertificateCreateModel model)
        {

            var cargoCertificate = await _cargoCertificateService.CreateAsync(model, User);

            if (cargoCertificate is null)
            {
                return BadRequest();
            }

            return Ok(cargoCertificate);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoCertificateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Put(Guid id, [FromBody] CargoCertificateUpdateModel model)
        {
            var cargoCertificate = await _cargoCertificateService.UpdateAsync(id, model, User);

            if (cargoCertificate is null)
            {
                return BadRequest();
            }

            return Ok(cargoCertificate);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _cargoCertificateService.DeleteAsync(id);
            return Ok();
        }

        [HttpGet("certificate/{id}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetPhoto(Guid id)
        {
            if (!await _azureBlobStorageService.ExistsAsync(BlobStorageFolderConstant.CargoCertificate, id))
            {
                return NotFound();
            }

            var download = await _azureBlobStorageService.DownloadAsync(BlobStorageFolderConstant.CargoCertificate, id);
            return File(download.Item1, download.Item2);
        }
    }
}

