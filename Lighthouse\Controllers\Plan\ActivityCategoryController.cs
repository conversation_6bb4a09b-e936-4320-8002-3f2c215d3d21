namespace Lighthouse.Controllers.Plan;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class ActivityCategoryController : ControllerBase
{
    private readonly IActvityCategoryService _activityCategoryService;
    private readonly IUserService _userService;

    public ActivityCategoryController(IActvityCategoryService activityCategoryService, IUserService userService)
    {
        _activityCategoryService = activityCategoryService;
        _userService = userService;
    }

    [HttpGet]
    [ProducesResponseType(200, Type = typeof(List<ActivityCategoryModel>))]
    [ProducesResponseType(401)]
    public async Task<IActionResult> GetActivityCategories()
    {
        var activityCategories = await _activityCategoryService.GetAsync();
        return Ok(activityCategories);
    }

    [HttpGet("activityCategory/{id}")]
    [ProducesResponseType(200, Type = typeof(ActivityCategoryModel))]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> GetActivityCategoryById(Guid id)
    {
        var activityCategory = await _activityCategoryService.GetActivityCategoryByIdAsync(id);
        return Ok(activityCategory);
    }

    [HttpPost]
    [ProducesResponseType(200, Type = typeof(ActivityCategoryModel))]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> Post([FromBody] ActivityCategoryUpsertModel model, Guid id)
    {
        var user = await _userService.GetCurrentUser(User);

        var activityCategory = await _activityCategoryService.CreateAsync(model, user.UserId);
        return Ok(activityCategory);
    }

    [HttpPut("{id}")]
    [ProducesResponseType(200, Type = typeof(ActivityCategoryModel))]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> Put(Guid id, [FromBody] ActivityCategoryUpsertModel model)
    {
        var user = await _userService.GetCurrentUser(User);
        var activityCategory = await _activityCategoryService.UpdateAsync(id, model, user.UserId);
        if (activityCategory is null)
        {
            return BadRequest("Failed to update activityCategory.");
        }

        return Ok(activityCategory);
    }

    [HttpDelete("{id}")]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _activityCategoryService.DeleteAsync(id);
        return Ok();
    }

}