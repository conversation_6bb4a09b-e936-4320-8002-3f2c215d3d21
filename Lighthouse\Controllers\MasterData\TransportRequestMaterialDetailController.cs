﻿namespace Lighthouse.Controllers.MasterData {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class TransportRequestMaterialDetailController : ControllerBase {
        private readonly ITransportRequestMaterialDetailService transportRequestMaterialDetailService;
        private readonly IUserService userService;

        public TransportRequestMaterialDetailController(ITransportRequestMaterialDetailService transportRequestMaterialDetailService, IUserService userService) {
            this.transportRequestMaterialDetailService = transportRequestMaterialDetailService;
            this.userService = userService;
        }

        [HttpGet("bytransportrequest/{transportRequestId}")]
        [ProducesResponseType(200, Type = typeof(IList<TransportRequestMaterialDetailModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByTransportRequestId(Guid transportRequestId) {
            var transportRequestMaterialDetails = await transportRequestMaterialDetailService.GetByTransportRequestIdAsync(transportRequestId);
            return Ok(transportRequestMaterialDetails);
        }

        [HttpGet("latestSubmittedVersionBytransportrequest/{transportRequestId}")]
        [ProducesResponseType(200, Type = typeof(IList<TransportRequestMaterialDetailModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetLatestSnapshotByTransportRequestId(Guid transportRequestId)
        {
            var materialDetails = await transportRequestMaterialDetailService.GetLatestSubmittedVersionByTransportRequestIdAsync(transportRequestId);
            return Ok(materialDetails);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(TransportRequestMaterialDetailModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var transportRequestMaterialDetail = await transportRequestMaterialDetailService.GetByIdAsync(id);
            if (transportRequestMaterialDetail == null) {
                return NotFound();
            }
            return Ok(transportRequestMaterialDetail);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(TransportRequestMaterialDetailModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] TransportRequestMaterialDetailUpsertModel model) {
            if (model is null) {
                return BadRequest("Invalid transport request material detail data");
            }
            UserModel currentUser = await userService.GetCurrentUser();
            var transportRequestMaterialDetail = await transportRequestMaterialDetailService.CreateAsync(model, currentUser);

            if (transportRequestMaterialDetail is null) {
                return BadRequest("Failed to create transport request material detail");
            }

            return Ok(transportRequestMaterialDetail);
        }

        [HttpPut("bulkupdate/{transportRequestId}")]
        [ProducesResponseType(200, Type = typeof(List<TransportRequestMaterialDetailModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> BulkUpdateAsync(Guid transportRequestId, List<TransportRequestMaterialDetailUpsertModel> finalTotalList)
        {
            UserModel currentUser = await userService.GetCurrentUser();
            var list = await transportRequestMaterialDetailService.BulkUpdateAsync(transportRequestId, finalTotalList, currentUser);
            return Ok(list);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(TransportRequestMaterialDetailModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] TransportRequestMaterialDetailUpsertModel model) {

            if (model is null) {
                return BadRequest("Invalid transport request material detail data");
            }
            UserModel currentUser = await userService.GetCurrentUser();
            var transportRequestMaterialDetail = await transportRequestMaterialDetailService.UpdateAsync(id, model, currentUser);

            if (transportRequestMaterialDetail is null) {
                return NotFound("Transport request material detail not found");
            }

            return Ok(transportRequestMaterialDetail);
        }
        
        [HttpPut("todoimdgcomplete/{id}")]
        [ProducesResponseType(200, Type = typeof(TransportRequestMaterialDetailModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateTransportRequestMaterialDetailImdgComplete(Guid id, [FromBody] TransportRequestMaterialDetailIMDGModel imdgModel) {
            
            var transportRequestMaterialDetail = await transportRequestMaterialDetailService.UpdateTransportRequestToDoImdgCompleteAsync(id, imdgModel);

            if (!transportRequestMaterialDetail)
            {
                return BadRequest("Failed to update IMDG");
            }

            return Ok(transportRequestMaterialDetail);
        }

        [HttpPut("comment")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateTransportRequestCargoComment([FromBody] TransportRequestMaterialDetailCommentModel cargoCommentModel)
        {
            var isCargoCancelled = await transportRequestMaterialDetailService.UpdateTransportRequestCargoCommentAsync(cargoCommentModel);
            if (!isCargoCancelled)
            {
                return BadRequest("Failed to add comment");
            }
            return Ok();
        }

        [HttpPut("cancel")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CancelTransportRequestCargos([FromBody] TransportRequestCancellationReasonModel cancellations) {
            var isCargoCancelled = await transportRequestMaterialDetailService.CancelAsync(cancellations);
            if (!isCargoCancelled) {
                return BadRequest("Failed to cancel transport request cargo");
            }
            return Ok();
        }

        [HttpDelete("bulkdelete")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(List<Guid> transportRequestMaterialIds) {
            var isTransportRequestMaterialDetailDeleted = await transportRequestMaterialDetailService.BulkDeleteAsync(transportRequestMaterialIds);
            if (!isTransportRequestMaterialDetailDeleted) {
                return BadRequest("Failed to delete transport request material detail");
            }
            return Ok();
        }

        [HttpPut("reinstate")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Reinstate([FromBody] List<Guid> transportRequestMaterialDetailIds) {
            await transportRequestMaterialDetailService.ReinstateAsync(transportRequestMaterialDetailIds);
            return Ok();
        }
        
        [HttpGet("exportmaterialdetailtable/{id}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> ExportTransportRequestCargos(Guid id) {
            try {
                var user = await userService.GetCurrentUser(User);
                byte[] data = await transportRequestMaterialDetailService.ExportTransportRequestMaterialDetails(id, user.LocationTimeZoneInfoId);

                return File(data, "application/download");
            } catch (Exception) {
                return NotFound();
            }
        }
    }
}
