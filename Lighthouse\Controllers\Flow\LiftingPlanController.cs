﻿namespace Lighthouse.Controllers.Flow
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class LiftingPlanController : ControllerBase
    {
        private readonly ILiftingPlanService _liftingPlanService;

        public LiftingPlanController(ILiftingPlanService liftingPlanService)
        {
            _liftingPlanService = liftingPlanService;
        }

        [HttpGet("list/{voyageId}")]
        [ProducesResponseType(200, Type = typeof(IList<LiftingPlanListModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllByVoyageId(Guid voyageId)
        {
            var liftingPlans = await _liftingPlanService.GetAllByVoyageId(voyageId);
            return Ok(liftingPlans);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(LiftingPlanModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var liftingPlan = await _liftingPlanService.GetByIdAsync(id);
            return Ok(liftingPlan);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(LiftingPlanModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.Foreman,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Post([FromBody] LiftingPlanUpsertModel model)
        {
            var liftingPlan = await _liftingPlanService.CreateAsync(model);
            return Ok(liftingPlan);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(LiftingPlanModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.Foreman,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Put(Guid id, [FromBody] LiftingPlanUpsertModel model)
        {
            var liftingPlan = await _liftingPlanService.UpdateAsync(id, model);
            return Ok(liftingPlan);
        }
    }
}

