namespace Lighthouse.Controllers.Flow;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class VoyageCargoInspectionAttachmentController : ControllerBase
{
    private readonly IVoyageCargoInspectionAttachmentService _voyageCargoInspectionAttachmentService;

    public VoyageCargoInspectionAttachmentController(
        IVoyageCargoInspectionAttachmentService voyageCargoInspectionAttachmentService)
    {
        _voyageCargoInspectionAttachmentService = voyageCargoInspectionAttachmentService;
    }
    
    [RequestFormLimits(MultipartBodyLengthLimit = Int32.MaxValue, ValueLengthLimit = Int32.MaxValue)]
    [DisableRequestSizeLimit]
    [HttpPost("{voyageCargoInspectionId}")]
    [ProducesResponseType(200, Type = typeof(Guid))]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<Guid>> Post(Guid voyageCargoInspectionId, [FromForm] VoyageCargoInspectionAttachmentCreateModel model)
    {
        
        return await _voyageCargoInspectionAttachmentService.AddAttachmentAsync(voyageCargoInspectionId, model, User);
    }

    [HttpGet("{voyageCargoInspectionId}")]
    [ProducesResponseType(200, Type = typeof(List<VoyageCargoInspectionAttachmentModel>))]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> GetTransportRequestAttachmentAsync(Guid voyageCargoInspectionId)
    {
        var downloads = await _voyageCargoInspectionAttachmentService.GetAttachmentAsync(voyageCargoInspectionId);

        if (downloads == null || !downloads.Any())
        {
            return Ok();
        }

        return Ok(downloads);
    }

    [HttpDelete("{blobId}/{voyageCargoInspectionId}")]
    [ProducesResponseType(200, Type = typeof(Guid))]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<Guid>> DeleteTransportRequestAttachmentAsync(Guid blobId, Guid voyageCargoInspectionId)
    {
        return await _voyageCargoInspectionAttachmentService.DeleteAttachmentAsync(blobId, voyageCargoInspectionId);
    }

    [HttpGet("{blobId}/{voyageCargoInspectionId}")]
    public async Task<IActionResult> DownloadDocument(Guid blobId, Guid voyageCargoInspectionId)
    {
        try
        {
            var (fileData, contentType, attachment) = await _voyageCargoInspectionAttachmentService.DownloadDocumentAsync(blobId, voyageCargoInspectionId);
            Response.Headers.Append("Content-Disposition", $"attachment; filename=\"{attachment.FileName}\"");
            return File(fileData, contentType);
        }
        catch
        {
            throw new Exception("Error encountered when retrieving attachment");
        }
    }
}