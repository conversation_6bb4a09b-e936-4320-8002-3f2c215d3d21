﻿namespace Lighthouse.Controllers.Flow
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VoyageCargoBulkController : ControllerBase
    {
        private readonly IVoyageCargoBulkService _voyageCargoBulkService;

        public VoyageCargoBulkController(IVoyageCargoBulkService voyageCargoBulkService)
        {
            _voyageCargoBulkService = voyageCargoBulkService;
        }

        [HttpGet("list/{voyageId}")]
        [ProducesResponseType(200, Type = typeof(IList<VoyageCargoBulkModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllByVoyageCargoId(Guid voyageId)
        {
            var voyageCargoBulks = await _voyageCargoBulkService.GetAllByVoyageIdAsync(voyageId);
            return Ok(voyageCargoBulks);
        }

        [HttpPut("bulkupdate/{voyageId}")]
        [ProducesResponseType(200, Type = typeof(List<VoyageCargoBulkModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> BulkUpdateAsync(Guid voyageId, List<VoyageCargoBulkUpsertModel> finalTotalList)
        {
            var list = await _voyageCargoBulkService.BulkUpdateAsync(voyageId, finalTotalList);
            return Ok(list);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoBulkModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var voyageCargoBulk = await _voyageCargoBulkService.GetByIdAsync(id);
            return Ok(voyageCargoBulk);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(VoyageCargoBulkModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Post([FromBody] VoyageCargoBulkUpsertModel model)
        {

            var voyageCargoBulk = await _voyageCargoBulkService.CreateAsync(model, User);

            if (voyageCargoBulk is null)
            {
                return BadRequest();
            }

            return Ok(voyageCargoBulk);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoBulkModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Put(Guid id, [FromBody] VoyageCargoBulkUpsertModel model)
        {
            var voyageCargoBulk = await _voyageCargoBulkService.UpdateAsync(id, model, User);

            if (voyageCargoBulk is null)
            {
                return BadRequest();
            }

            return Ok(voyageCargoBulk);
        }

        [HttpPut("bulkcancel")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> BulkCancel(List<Guid> ids)
        {
            await _voyageCargoBulkService.BulkCancelAsync(ids);
            return Ok();
        }

        [HttpDelete("bulkdelete")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> BulkDelete(List<Guid> ids)
        {
            await _voyageCargoBulkService.BulkDeleteAsync(ids);
            return Ok();
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _voyageCargoBulkService.DeleteAsync(id);
            return Ok();
        }
    }
}

