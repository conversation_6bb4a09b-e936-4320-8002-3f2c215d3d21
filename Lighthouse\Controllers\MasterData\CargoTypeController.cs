﻿namespace Lighthouse.Controllers.MasterData
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CargoTypeController : ControllerBase
    {
        private readonly ICargoTypeService service;

        public CargoTypeController(ICargoTypeService service)
        {
            this.service = service;
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<CargoTypeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var cargoTypes = await service.GetAllAsync();
            return Ok(cargoTypes);
        }

        [HttpGet("byFamily")]
        [ProducesResponseType(200, Type = typeof(IList<CargoTypeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByFamily([FromQuery] Guid familyId)
        {
            var cargoFamilyTypes = await service.GetByFamilyIdAsync(familyId);
            return Ok(cargoFamilyTypes);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoTypeModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Get(Guid id)
        {
            var cargoType = await service.GetByIdAsync(id);
            return Ok(cargoType);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CargoTypeModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Post([FromBody] CargoTypeUpsertModel model)
        {
            var cargoType = await service.CreateAsync(model, User);

            if (cargoType is null)
            {
                return BadRequest();
            }

            return Ok(cargoType);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoTypeModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Put(Guid id, [FromBody] CargoTypeUpsertModel model)
        {
            var cargoType = await service.UpdateAsync(id, model, User);
            return Ok(cargoType);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await service.DeleteAsync(id);
            return Ok();
        }

        [HttpPost("enableDisable/{id}")]
        [ProducesResponseType(200, Type = typeof(CargoTypeModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> EnableDisableCargoType(Guid id)
        {
            var type = await service.EnableDisableCargoType(id);
            return Ok(type);
        }
    }
}
