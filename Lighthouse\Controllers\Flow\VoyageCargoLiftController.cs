﻿namespace Lighthouse.Controllers.Flow
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VoyageCargoLiftController : ControllerBase
    {
        private readonly IVoyageCargoLiftService _voyageCargoLiftService;

        public VoyageCargoLiftController(IVoyageCargoLiftService voyageCargoLiftService)
        {
            _voyageCargoLiftService = voyageCargoLiftService;
        }

        [HttpGet("getallbyvoyagecargoid/{voyageCargoId}")]
        [ProducesResponseType(200, Type = typeof(IList<VoyageCargoLiftModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllByVoyageCargoId(Guid voyageCargoId)
        {
            var voyageCargoLifts = await _voyageCargoLiftService.GetAllByVoyageCargoIdAsync(voyageCargoId);
            return Ok(voyageCargoLifts);
        }

        [HttpGet("getallbyvoyageid/{voyageId}")]
        [ProducesResponseType(200, Type = typeof(List<VoyageCargoLiftListingModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllByVoyageId(Guid voyageId)
        {
            var voyageCargoLifts = await _voyageCargoLiftService.GetAllByVoyageIdAsync(voyageId);
            return Ok(voyageCargoLifts);
        }

        [HttpPut("updateweight/{voyageCargoLiftId}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoLiftModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.Foreman,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<VoyageCargoLiftModel> UpdateWeightAsync(Guid voyageCargoLiftId, [FromBody] VoyageCargoLiftUpdateModel model)
        {
            return await _voyageCargoLiftService.UpdateWeightAsync(voyageCargoLiftId, model);
        }

        [HttpPut("updatearea/{voyageCargoLiftId}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoLiftModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.Foreman,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<VoyageCargoLiftModel> UpdateArreaAsync(Guid voyageCargoLiftId, [FromBody] VoyageCargoLiftUpdateModel model)
        {
            return await _voyageCargoLiftService.UpdateAreaAsync(voyageCargoLiftId, model);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoLiftModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var voyageCargoLift = await _voyageCargoLiftService.GetByIdAsync(id);
            return Ok(voyageCargoLift);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(VoyageCargoLiftModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.Foreman,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Post([FromBody] VoyageCargoLiftCreateModel model)
        {

            var voyageCargoLift = await _voyageCargoLiftService.CreateAsync(model);

            if (voyageCargoLift is null)
            {
                return BadRequest();
            }

            return Ok(voyageCargoLift);
        }

        [HttpPut("createandcomplete")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoLiftModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.Foreman,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> CreateAndCompleteAsync([FromBody] VoyageCargoLiftCreateModel model)
        {
            await _voyageCargoLiftService.CreateAndCompleteAsync(model);
            return NoContent();
        }

        [HttpPut("setactivate/{id}")]
        [ProducesResponseType(200, Type = typeof(VoyageCargoLiftModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.Foreman,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
        public async Task<IActionResult> Put(Guid id,[FromQuery] bool activate)
        {
            var voyageCargoLift = await _voyageCargoLiftService.SetActivate(id, activate);

            if (voyageCargoLift is null)
            {
                return BadRequest();
            }

            return Ok(voyageCargoLift);
        }
    }
}

