﻿namespace Lighthouse.Extensions
{
    public static class StartupExtensions
    {
        #region Services
        public static SecretClient ConfigureKeyVault(this IServiceCollection services, IConfiguration configuration)
        {
            var credential = new DefaultAzureCredential();
            var keyVaultSection = configuration.GetSection("AzureKeyVault");
            var keyVaultUri = keyVaultSection.GetValue<string>("KeyVaultUri");
            return new SecretClient(new Uri(keyVaultUri), credential);
        }

        public static void AddCache(this IServiceCollection services, SecretClient keyVaultClient)
        {
            services.AddSingleton<IConnectionMultiplexer>(ConnectionMultiplexer.Connect(keyVaultClient.GetSecret("redis-connectionstring").Value.Value));
            services.AddTenantAwareCache();
        }

        public static void AddSecurityConfigs(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            }).AddJwtBearer(options =>
            {
                options.Authority = configuration.GetValue<string>("Security:Authority");
                options.Audience = configuration.GetValue<string>("Security:Audience");
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidIssuer = configuration.GetValue<string>("Security:Authority"),
                    ValidateAudience = true,
                    ValidAudience = configuration.GetValue<string>("Security:Audience"),
                    ValidateLifetime = true
                };
            });
        }

        public static void AddSwagger(this IServiceCollection services)
        {
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo { Title = "Lighthouse API", Version = "v1" });

                c.EnableAnnotations();

                c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement() {
                    {
                        new OpenApiSecurityScheme {
                            Reference = new OpenApiReference {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            },
                            Scheme = "oauth2",
                            Name = "Bearer",
                            In = ParameterLocation.Header,
                    },
                        new List<string>()
                    }
                });
            });
        }

        public static void AddTenantsInformation(this IServiceCollection services, IConfiguration configuration, SecretClient keyVaultClient, Dictionary<string, TenantConfigurationSettings> tenantConfigurationSettings)
        {
            //getting all connection strings from keyvault so we don't get them with each request coming to the server
            var publicTenants = keyVaultClient.GetSecret("all-tenants").Value.Value.Split(',');
            if (!EF.IsDesignTime && !Debugger.IsAttached)
                foreach (var tenant in publicTenants)
                {
                    var tenantConfigSetting = new TenantConfigurationSettings();
                    tenantConfigSetting.TenantName = tenant;
                    tenantConfigSetting.DatabaseConnectionString = keyVaultClient.GetSecret($"connectionstring-{tenant}").Value.Value;
                    tenantConfigSetting.StorageContainer = keyVaultClient.GetSecret($"storagecontainer-{tenant}").Value.Value;
                    tenantConfigSetting.StorageConnectionString = keyVaultClient.GetSecret($"storageconnectionstring-{tenant}").Value.Value;
                    tenantConfigurationSettings.Add(tenant, tenantConfigSetting);
                }
            var localTenants = configuration.GetSection("LocalTenantsSettings").Get<LocalTenantsSettings>()?.LocalTenants ?? new();
            foreach (var localTenant in localTenants)
            {
                localTenant.IsLocal = true;
                if (string.IsNullOrEmpty(localTenant.DatabaseConnectionString))
                    localTenant.DatabaseConnectionString = $"Server=localhost;Initial Catalog={localTenant.TenantName};Trusted_Connection=true;TrustServerCertificate=True;";
                tenantConfigurationSettings.Add(localTenant.TenantName, localTenant);
            }

            services.AddSingleton<TenantsInformation>();
        }

        public static void AddDataContext(this IServiceCollection services, Dictionary<string, TenantConfigurationSettings> tenantConfigurationSettings) {
            services.AddDbContext<DataContext>((serviceProvider, dbContextBuilder) =>
            {
                var httpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();
                var httpContext = httpContextAccessor.HttpContext;

                string connectionString = "";

                if (httpContext != null) {
                    if (httpContext.Items.TryGetValue("TENANT_NAME", out object tenantName))
                        connectionString = tenantConfigurationSettings[tenantName.ToString().ToLower()].DatabaseConnectionString;
                }

                if (EF.IsDesignTime)
                    connectionString = tenantConfigurationSettings.First().Value.DatabaseConnectionString;

                dbContextBuilder.UseSqlServer(connectionString, options =>
                {
                    options.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                    options.CommandTimeout(900);
                });
            });

            services.AddTransient<DataInitializer>();
        }


        public static void AddGraphQLConfiguration(this IServiceCollection services)
        {
            services.AddGraphQLServer("FlowVoyageSchema")
                .AddQueryType<FlowVoyageQuery>()
                .AddProjections()
                .AddFiltering()
                .AddSorting()
                .AddAuthorization();
        }

        public static void AddBlobStorage(this IServiceCollection services, Dictionary<string, TenantConfigurationSettings> tenantConfigurationSettings)
        {
            // storage service
            services.AddScoped<IStorage, BlobStorage>((serviceProvider) =>
            {
                var httpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();
                var httpContext = httpContextAccessor.HttpContext;

                string storageConnectionString = "";
                string storageContainer = "";

                BlobStorage blobStorage = null;

                if (httpContext != null)
                {
                    if (httpContext.Items.TryGetValue("TENANT_NAME", out object tenantName) &&
                        tenantConfigurationSettings.TryGetValue(tenantName.ToString().ToLower(), out var tenantConfigurationSetting))
                    {
                        storageConnectionString = tenantConfigurationSetting.StorageConnectionString;
                        storageContainer = tenantConfigurationSetting.StorageContainer;
                        blobStorage = new BlobStorage(storageConnectionString, storageContainer);
                    }
                }
                return blobStorage;
            });
            services.AddScoped<IAzureBlobStorageService, AzureBlobStorageService>();
        }

        public static void AddControllersAndRoutingEssentials(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddLogging(loggingBuilder =>
            {
                loggingBuilder.AddConfiguration(configuration.GetSection("Logging"));
                loggingBuilder.AddConsole();
                loggingBuilder.AddDebug();
            });

            services.AddControllers().AddTimeZoneDateTimeConversion();
            services.AddRouting(s => s.LowercaseUrls = true);
            services.AddHttpClient();
            services.AddCors();
            services.AddApplicationInsightsTelemetry();
            services.AddHttpContextAccessor();

            // compression and caching
            services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Fastest;
            });
            services.AddResponseCompression();
        }
        #endregion

        #region Configuration
        public static void FillCache(this IApplicationBuilder app, Dictionary<string, TenantConfigurationSettings> tenantConfigurationSettings)
        {
            //filling cache with user roles
            foreach (var tenantConfigSetting in tenantConfigurationSettings)
            {
                using (var scope = app.ApplicationServices.CreateScope())
                {
                    var context = scope.ServiceProvider.GetRequiredService<DataContext>();
                    var redis = scope.ServiceProvider.GetRequiredService<IConnectionMultiplexer>().GetDatabase();

                    context.Database.SetConnectionString(tenantConfigSetting.Value.DatabaseConnectionString);
                    if (tenantConfigSetting.Value.IsLocal || !Debugger.IsAttached)
                        context.Database.Migrate();

                    var dataInitializer = scope.ServiceProvider.GetService<DataInitializer>();
                    dataInitializer.Initialize();

                    var users = context.Users.ToList();
                    foreach (var user in users)
                    {
                        var cacheValue = System.Text.Json.JsonSerializer.Serialize(user.Roles);
                        redis.StringSet($"{tenantConfigSetting.Key.ToLower()}:user:role:{user.EmailAddress.ToLower()}", cacheValue);
                    }
                }
            }
        }

        public static void AddEndpoints(this IApplicationBuilder app)
        {
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapGraphQL("/api/flowvoyage/query", "FlowVoyageSchema");
            });
        }

        public static void MigrateTenantDatabases(this IApplicationBuilder app, Dictionary<string, TenantConfigurationSettings> tenantConfigurationSettings)
        {
            //filling tenant information
            using (var scope = app.ApplicationServices.CreateScope())
            {
                var tenantInformations = scope.ServiceProvider.GetRequiredService<TenantsInformation>();
                //gained from Appsettings
                var authInfo = scope.ServiceProvider.GetRequiredService<IOptions<Auth0Settings>>().Value;
                var auth0ManagementClientFactory = scope.ServiceProvider.GetRequiredService<Auth0ManagementApiClientFactory>();
                using var managementClient = auth0ManagementClientFactory.GetManagementApiClientAsync().Result;
                //this gets all the organisations 
                var organizations = managementClient.Organizations.GetAllAsync(new PaginationInfo(0, 100)).Result;
                var tenantsInfo = organizations
                    .Where(x => x.Id != authInfo.AdminOrganizationId && tenantConfigurationSettings.Keys.Contains(x.Name)) //not admin and mentioned in keyvault or local tenants
                    .ToDictionary(x => x.Name.ToLower(), x => new TenantInfo()
                    {
                        DisplayName = x.DisplayName,
                        Name = x.Name,
                        Id = x.Id,
                        ConnectionString = tenantConfigurationSettings[x.Name].DatabaseConnectionString,
                    });
                tenantInformations.Tenants = tenantsInfo;
                tenantInformations.AdminOrganization = organizations.Where(x => x.Id == authInfo.AdminOrganizationId).Select(x => new TenantInfo()
                {
                    DisplayName = x.DisplayName,
                    Name = x.Name,
                    Id = x.Id
                }).Single();
            }
        }
        public static void ConfigureSwagger(this IApplicationBuilder app)
        {
            app.UseSwagger(c =>
            {
                c.PreSerializeFilters.Add((swagger, httpReq) =>
                {
                    var server = new OpenApiServer() { Url = $"{httpReq.Scheme}://{httpReq.Host.Value}" };
                    swagger.Servers = new List<OpenApiServer>() { server };
                });
            });
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Lighthouse.ApiApp v1"));
        }
        #endregion
    }
}
