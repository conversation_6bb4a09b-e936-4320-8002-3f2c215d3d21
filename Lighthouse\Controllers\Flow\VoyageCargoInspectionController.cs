namespace Lighthouse.Controllers.Flow;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class VoyageCargoInspectionController : ControllerBase
{

    private readonly IVoyageCargoInspectionService _voyageCargoInspectionService;
    private readonly IUserService _userService;

    public VoyageCargoInspectionController(IVoyageCargoInspectionService voyageCargoInspectionService, IUserService userService)
    {
        _voyageCargoInspectionService = voyageCargoInspectionService;
        _userService = userService;
    }

    [HttpPut("{id}")]
    [ProducesResponseType(200, Type = typeof(Guid))]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [AuthorizeRoles(
            UserRoleConstant.Admin,
            UserRoleConstant.SupportUser,
            UserRoleConstant.ForemanInspector,
            UserRoleConstant.BackloadController,
            UserRoleConstant.CargoInspector)]
    public async Task<Guid> Put(Guid id, [FromBody] VoyageCargoInspectionUpsertModel model)
    {
        var user = await _userService.GetCurrentUser(User);
        if (user == null)
        {
            throw new UnauthorizedAccessException();
        }
        var voyageId = await _voyageCargoInspectionService.UpdateVoyageCargoInspection(id, model, user.UserId);
        return voyageId;
    }
}