﻿namespace Lighthouse.Controllers.MasterData
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CargoFamilyController : ControllerBase
    {
        private readonly ICargoFamilyService service;

        public CargoFamilyController(ICargoFamilyService service)
        {
            this.service = service;
        }

        [HttpGet()]
        [ProducesResponseType(200, Type = typeof(IList<CargoFamilyModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var cargoFamilies = await service.GetAllAsync();
            return Ok(cargoFamilies);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoFamilyModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Get(Guid id)
        {
            var cargoFamily = await service.GetByIdAsync(id);
            return Ok(cargoFamily);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CargoFamilyModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Post([FromBody] CargoFamilyUpsertModel model)
        {
            var cargoFamily = await service.CreateAsync(model, User);

            if (cargoFamily is null)
            {
                return BadRequest();
            }

            return Ok(cargoFamily);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoFamilyModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Put(Guid id, [FromBody] CargoFamilyUpsertModel model)
        {
            var cargoFamily = await service.UpdateAsync(id, model, User);
            return Ok(cargoFamily);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await service.DeleteAsync(id);
            return Ok();
        }

        [HttpPost("enableDisable/{id}")]
        [ProducesResponseType(200, Type = typeof(CargoFamilyModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> EnableDisableCargoFamily(Guid id)
        {
            var cargoFamily = await service.EnableDisableCargoFamily(id);
            return Ok(cargoFamily);
        }

        [HttpGet("getCargoFamilyTypeSizeName/{id}")]
        [ProducesResponseType(200, Type = typeof(List<string>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> GetCargoFamilyTypeSizeNames(Guid id)
        {
            var cargoFamilyTypeSizeNames = await service.GetCargoFamilyTypeSizeNamesAsync(id);
            return Ok(cargoFamilyTypeSizeNames);
        }
    }
}
