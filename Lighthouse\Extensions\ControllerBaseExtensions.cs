﻿namespace Lighthouse.Extensions {
    public static class ControllerBaseExtensions {

        public static IActionResult ErrorResult(this ControllerBase controller, string code, string description) {
            return controller.BadRequest(new ErrorResponse[]
            {
                new ErrorResponse(code, description)
            });
        }

        public static IActionResult ErrorResult(this ControllerBase controller, IEnumerable<IdentityError> errors) {
            return controller.BadRequest(errors);
        }

        public static IActionResult CreateActionResult<T>(
            this ControllerBase controller,
            ServiceResponse<T> response,
            HttpStatusCode successStatusCode = HttpStatusCode.OK,
            bool includeResponseData = true) {
            if (response.HaveErrors) {
                var errorsObject = response.GetErrorsObject;

                return controller.BadRequest(errorsObject);
            }

            if (includeResponseData) {
                return controller.StatusCode((int)successStatusCode, response.Response);
            }

            return controller.StatusCode((int)successStatusCode);
        }

        public static IActionResult CreateActionResult(
            this ControllerBase controller,
            ServiceResponse response,
            HttpStatusCode successStatusCode = HttpStatusCode.OK) {
            if (response.HaveErrors) {
                var errorsObject = response.GetErrorsObject;

                return controller.BadRequest(errorsObject);
            }

            return controller.StatusCode((int)successStatusCode);
        }
    }
}
