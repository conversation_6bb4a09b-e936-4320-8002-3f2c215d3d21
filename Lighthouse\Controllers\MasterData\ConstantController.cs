﻿namespace Lighthouse.Controllers.MasterData
{

    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ConstantController: ControllerBase {

        private readonly IUserService userService;

        public ConstantController(IUserService userService) {
            this.userService = userService;
        }

        [HttpGet("userroles")]
        [ProducesResponseType(200, Type = typeof(IList<RoleModel>))]
        public async Task<IActionResult> GetUserRoles() {

            var user = await userService.GetCurrentUser(User);

            return Ok(user.Roles);
        }

        [HttpGet("assettypes")]
        [ProducesResponseType(200 , Type = typeof(IList<ConstantModel>))]
        public IActionResult GetAssetTypes() {

            IEnumerable<ConstantModel> constants = AssetTypeConstant.ValuesAndDescriptions
                .Select(x => new ConstantModel {
                    Value = x.Key ,
                    Description = x.Value
                });

            return Ok(constants);
        }

        [HttpGet("transactiontypes")]
        [ProducesResponseType(200, Type = typeof(IList<ConstantModel>))]
        public IActionResult GetTransactionTypes() {

            IEnumerable<ConstantModel> constants = TransactionTypeConstant.ValuesAndDescriptions
                .Select(x => new ConstantModel {
                    Value = x.Key,
                    Description = x.Value
                });

            return Ok(constants);
        } 
        
        [HttpGet("chargeabilitytypes")]
        [ProducesResponseType(200, Type = typeof(IList<ConstantModel>))]
        public IActionResult GetChargeabilityTypes() {

            IEnumerable<ConstantModel> constants = ChargeabilityConstant.GetValuesAndDescriptions()
                .Select(x => new ConstantModel {
                    Value = x.Key,
                    Description = x.Value
                });

            return Ok(constants);
        }

        [HttpGet("bulkfluidtypes")]
        [ProducesResponseType(200, Type = typeof(IList<ConstantModel>))]
        public IActionResult GetBulkFluidTypes() {

            IEnumerable<ConstantModel> constants = BulkFluidTypeConstant.GetValuesAndDescriptions()
                .Select(x => new ConstantModel {
                    Value = x.Key,
                    Description = x.Value
                });

            return Ok(constants);
        }

        [HttpGet("activityTypes")]
        [ProducesResponseType(200, Type = typeof(IList<ConstantModel>))]
        public IActionResult GetActivityTypes() {

            IEnumerable<ConstantModel> constants = ActivityTypeConstant.GetValuesAndDescriptions()
                .Select(x => new ConstantModel {
                    Value = x.Key,
                    Description = x.Value
                });

            return Ok(constants);
        }

        [HttpGet("applicationRoles")]
        [ProducesResponseType(200, Type = typeof(IList<ApplicationRolesModel>))]
        public IActionResult GetApplications()
        {
            IEnumerable<ApplicationRolesModel> applications = UserRoleConstant.GetApplicationRoleModels();
            return Ok(applications);
        }

    }
}
